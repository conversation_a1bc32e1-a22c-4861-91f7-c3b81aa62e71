import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/video.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'video_coupon_state.dart';

class VideoCouponCubit extends Cubit<VideoCouponState> {
  VideoCouponCubit() : super(const VideoCouponState()) {
    fetchRulePicture();
  }

  void fetchRulePicture() async {
    emit(state.copyWith(rulePictureState: NetState.loading));
    final res = await GlobalConfig().getPictureConfig();
    if (res != null) {
      emit(state.copyWith(rulePictureState: NetState.success, rulePicture: res.videoWatchRule));
    } else {
      emit(state.copyWith(rulePictureState: NetState.failed));
    }
  }

  userOnClickSubmit(TextEditingController textController) async {
    sl<NavigatorService>().unFocus();

    if (textController.text.isEmpty) return GSEasyLoading.showToast("redeem_code_hint".tr());
    GSEasyLoading.showLoading();
    final flag = await VideoApi.submitVideoCoupon(coupon: textController.text);
    textController.clear();
    if (flag) {
      await sl<UserCubit>().fetchVideoVipInfo();
      GSEasyLoading.showToast("兑换成功");
    }
    GSEasyLoading.dismiss();
  }

  void resetState() => emit(const VideoCouponState());
}
