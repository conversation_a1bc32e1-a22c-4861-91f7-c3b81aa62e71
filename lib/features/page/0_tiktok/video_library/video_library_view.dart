import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/notification/notification_badge.dart';
import 'package:wd/shared/widgets/tiktok/home_video_library_filter.dart';
import 'package:wd/shared/widgets/tiktok/video_search_bar.dart';
import 'package:wd/shared/widgets/tiktok/video_sliver_grid_view.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../core/base/base_state.dart';
import 'video_library_cubit.dart';
import 'video_library_state.dart';

class VideoLibraryPage extends BasePage {
  const VideoLibraryPage({super.key});

  @override
  BasePageState<BasePage> getState() => _VideoLibraryPageState();
}

class _VideoLibraryPageState extends BasePageState<VideoLibraryPage> {
  final paddingH = 20.gw;

  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    pageTitle = "18+";
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    navColor = context.theme.scaffoldBackgroundColor;
  }

  void _onLoading() {
    context.read<VideoLibraryCubit>().updatePageNoToNext();
    context.read<VideoLibraryCubit>().fetchVideoDataList();
  }

  void _listener(BuildContext context, VideoLibraryState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    final cubit = context.read<VideoLibraryCubit>();

    return BlocConsumer<VideoLibraryCubit, VideoLibraryState>(
        listener: _listener,
        builder: (context, state) {
          if (state.netState == NetState.showReload) {
            return Container(
                color: Theme.of(context).scaffoldBackgroundColor,
                child: NetErrorWidget(title: 'network_error'.tr(), refreshMethod: () => cubit.fetchData()));
          }

          return Container(
            color: context.theme.scaffoldBackgroundColor,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: paddingH),
              child: CommonRefresher(
                bgColor: Theme.of(context).scaffoldBackgroundColor,
                enablePullDown: false,
                enablePullUp: true,
                refreshController: refreshController,
                onRefresh: null,
                onLoading: _onLoading,
                listWidget: CustomScrollView(
                  slivers: [
                    // 搜索框
                    SliverToBoxAdapter(
                      child: InkWell(
                        onTap: () => cubit.goToSearchPage(),
                        child: VideoSearchBar(
                          hintText: 'please_enter_video_name'.tr(),
                          enable: false,
                          controller: state.searchController,
                        ),
                      ),
                    ),
                    SliverToBoxAdapter(child: SizedBox(height: 15.gw)),
                    // 筛选视图
                    if (state.filterCategories.isNotEmpty) ...[
                      SliverToBoxAdapter(
                        child: AnimationLimiter(
                          child: HomeVideoLibraryFilterView(
                            current: state.currentVideoCategory,
                            dataList: state.filterCategories,
                            onFilterSelected: (item) {
                              context.read<VideoLibraryCubit>().selectFilter(item);
                            },
                          ),
                        ),
                      ),
                    ],

                    if (state.listNetState == NetState.empty) ...[
                      SliverToBoxAdapter(
                        child: SizedBox(
                          height: 400.gw,
                          child: const EmptyWidget(),
                        ),
                      ),
                    ] else ...[
                      // 视频列表
                      SliverPadding(
                        padding: EdgeInsets.only(top: 15.gw),
                        sliver: VideoSliverGridView(
                          key: const ValueKey('video_grid_view'),
                          paddingH: 24.gw,
                          videoList: state.videoList,
                          onTapCell: (videoEntity) {
                            // AuthUtil.checkIfLogin(() {
                            sl<NavigatorService>().push(AppRouter.videoDetail,
                                arguments: {"model": videoEntity, "videoCategory": state.currentVideoCategory.title});
                            // });
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        });
  }
}
