import 'dart:async';

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/models/entities/login_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';

import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/open_install_manager/open_install_manager.dart';
import 'package:wd/features/page/login/login_state.dart';
import 'package:wd/features/page/login/register/register_state.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../../../../core/constants/enums.dart';
import '../../../../shared/widgets/wangyi_captcha/wangyi_captcha.dart';
import '../../../../core/utils/string_util.dart';

class RegisterCubit extends Cubit<RegisterState> {
  RegisterCubit()
      : super(RegisterState(
          verificationCodeController: TextEditingController(),
          phoneController: TextEditingController(),
          emailController: TextEditingController(),
          smsCodeController: TextEditingController(),
          usernameController: TextEditingController(),
          passwordController: TextEditingController(),
          confirmPasswordController: TextEditingController(),
        )) {
    fetchCaptchaType();
    initConfig();
    _initializeCurrency();
    _initializeCountry();

    if (!kEnablePrivacyPolicySwitch) {
      emit(state.copyWith(acceptPrivacyPolicy: true));
    }
  }

  /// Initialize default country
  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    emit(state.copyWith(selectedCountry: defaultCountry));
  }

  Future<void> _initializeCurrency() async {
    var defaultCurrency = GlobalConfig().systemConfig.currencyList.firstWhereOrNull((e) => e.isDefault == 0);
    defaultCurrency ??= GlobalConfig().systemConfig.currencyList.first;
    emit(state.copyWith(selectCurrency: defaultCurrency));
  }

  bool _isSwitching = false; // 添加切换状态标志位
  Timer? _captchaTimer;

  void setUsername(String username) {
    emit(state.copyWith(username: username));
  }

  void setPhone(String phone) {
    emit(state.copyWith(phone: phone));
  }

  void setEmail(String email) {
    emit(state.copyWith(email: email));
  }

  void setSmsCode(String smsCode) {
    emit(state.copyWith(smsCode: smsCode));
  }

  void setVerificationCode(String verificationCode) {
    emit(state.copyWith(verificationCode: verificationCode));
  }

  void setPassword(String password) {
    emit(state.copyWith(password: password));
  }

  void setConfirmPassword(String confirmPassword) {
    emit(state.copyWith(confirmPassword: confirmPassword));
  }

  /// Update selected country
  void updateSelectedCurrency(CurrencyConfig currency) {
    emit(state.copyWith(selectCurrency: currency));
  }

  /// Update selected country
  void updateSelectedCountry(Country country) {
    emit(state.copyWith(selectedCountry: country));
  }

  void setInviteCode(String inviteCode) {
    emit(state.copyWith(inviteCode: inviteCode));
  }

  void setChannelCodeCode(String channelCode) {
    emit(state.copyWith(channelCode: channelCode));
  }

  void togglePasswordVisibility() {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  void toggleAcceptPrivacyPolicy(bool acceptPrivacyPolicy) {
    emit(state.copyWith(acceptPrivacyPolicy: acceptPrivacyPolicy));
  }

  /// 获取验证类型
  fetchCaptchaType() async {
    final registerCaptchaType = await GlobalConfig().getConfigValueByKey("register_captcha");
    if (registerCaptchaType != null) {
      final captchaType = CaptchaType.values.firstWhere(
        (type) {
          return type.index.toString() == registerCaptchaType;
        },
        orElse: () => CaptchaType.wangYi,
      );
      emit(state.copyWith(captchaType: captchaType));
    }
    if (state.captchaType == CaptchaType.picture) {
      fetchImageCaptcha();
    }
  }

  bool isFetchingCaptcha = false;

  int _captchaRefreshCount = 0;
  DateTime? _lastRefreshTime;

  void fetchImageCaptcha() async {
    if (state.captchaType != CaptchaType.picture) return;

    if (_captchaRefreshCount >= 3) {
      final now = DateTime.now();
      if (_lastRefreshTime != null && now.difference(_lastRefreshTime!).inSeconds < 10) {
        GSEasyLoading.showToast("do_not_refresh_frequently".tr());
        return;
      }
    }

    if (isFetchingCaptcha) return;
    isFetchingCaptcha = true;
    _captchaRefreshCount++;
    _lastRefreshTime = DateTime.now();

    final result = await UserApi.fetchCaptcha();
    if (result != null && !isClosed) {
      emit(state.copyWith(captchaModel: result));
    }
    isFetchingCaptcha = false;
  }

  @override
  Future<void> close() {
    _captchaTimer?.cancel();
    return super.close();
  }

  void _showWangYiCaptcha({
    required String account,
    required Function(String) onSuccess,
  }) {
    WangYiCaptcha().show(
      account: account,
      captchaId: kWangYiVerityKey,
      onSuccess: onSuccess,
      onValidateFailClose: () {
        if (!isClosed) {
          emit(state.copyWith(registerStatus: NetState.idle));
        }
      },
      onError: () {
        GSEasyLoading.showToast('verification_failed'.tr());
        emit(state.copyWith(registerStatus: NetState.failed));
      },
    );
  }

  Future<void> initConfig() async {
    var systemConfig = GlobalConfig().systemConfig;

    final registerMethod = AuthMethodType.fromConfig(systemConfig.loadLoginAndRegWay.register);
    final initialType = getInitialType(systemConfig.loadLoginAndRegWay.defRegister);

    emit(state.copyWith(
      authMethodType: registerMethod,
      registerType: initialType,
    ));
  }

  LoginType getInitialType(String defRegister) {
    switch (defRegister) {
      case '1':
        return LoginType.phone;
      case '2':
        return LoginType.userName;
      case '3':
        return LoginType.email;
      default:
        return LoginType.userName;
    }
  }

  void switchRegisterType() {
    // 如果正在切换中，直接返回
    if (_isSwitching) return;
    // 隐藏键盘
    sl<NavigatorService>().unFocus();
    _isSwitching = true; // 标记开始切换

    LoginType newType;
    if (state.registerType == LoginType.userName) {
      newType = state.authMethodType.supportsPhone ? LoginType.phone : LoginType.userName;
    } else if (state.registerType == LoginType.phone) {
      newType = state.authMethodType.supportsAccount ? LoginType.userName : LoginType.phone;
    } else if (state.registerType == LoginType.email) {
      if (state.authMethodType.supportsAccount) {
        newType = LoginType.userName;
      } else if (state.authMethodType.supportsPhone) {
        newType = LoginType.phone;
      } else {
        newType = LoginType.email;
      }
    } else {
      newType = LoginType.userName; // Default fallback
    }

    state.usernameController.clear();
    state.passwordController.clear();
    state.verificationCodeController.clear();
    state.phoneController.clear();
    state.emailController.clear();
    state.smsCodeController.clear();
    state.confirmPasswordController.clear();

    emit(state.copyWith(
      username: '',
      phone: '',
      email: '',
      password: '',
      smsCode: '',
      verificationCode: null,
      registerStatus: NetState.idle,
      registerType: newType,
    ));
    // 使用 Future.delayed 确保状态更新完成
    Future.delayed(const Duration(milliseconds: 300), () {
      _isSwitching = false; // 标记切换完成
    });
  }

  // Create a method to switch specifically to email
  void switchToEmailRegister() {
    // 如果正在切换中，直接返回
    if (_isSwitching) return;
    // 隐藏键盘
    sl<NavigatorService>().unFocus();
    _isSwitching = true; // 标记开始切换

    state.usernameController.clear();
    state.passwordController.clear();
    state.verificationCodeController.clear();
    state.phoneController.clear();
    state.emailController.clear();
    state.smsCodeController.clear();
    state.confirmPasswordController.clear();

    emit(state.copyWith(
      username: '',
      phone: '',
      email: '',
      password: '',
      smsCode: '',
      verificationCode: null,
      registerStatus: NetState.idle,
      registerType: LoginType.email,
    ));
    // 使用 Future.delayed 确保状态更新完成
    Future.delayed(const Duration(milliseconds: 300), () {
      _isSwitching = false; // 标记切换完成
    });
  }

  // Create a method to switch specifically to phone from email
  void switchToPhoneRegister() {
    if (_isSwitching) return;
    sl<NavigatorService>().unFocus();
    _isSwitching = true;

    state.usernameController.clear();
    state.passwordController.clear();
    state.verificationCodeController.clear();
    state.phoneController.clear();
    state.emailController.clear();
    state.smsCodeController.clear();
    state.confirmPasswordController.clear();

    emit(state.copyWith(
      username: '',
      phone: '',
      email: '',
      password: '',
      smsCode: '',
      verificationCode: null,
      registerStatus: NetState.idle,
      registerType: LoginType.phone,
    ));
    Future.delayed(const Duration(milliseconds: 300), () {
      _isSwitching = false;
    });
  }

  void register({LoginType? loginType}) async {
    sl<NavigatorService>().unFocus();

    // Privacy policy validation
    if (kEnablePrivacyPolicySwitch && !state.acceptPrivacyPolicy) {
      GSEasyLoading.showToast('please_agree_privacy_policy'.tr());
      return;
    }

    if (loginType == LoginType.phone && !state.authMethodType.supportsPhone) {
      GSEasyLoading.showToast('phone_registration_not_supported'.tr());
      return;
    }
    if (loginType == LoginType.userName && !state.authMethodType.supportsAccount) {
      GSEasyLoading.showToast('account_registration_not_supported'.tr());
      return;
    }

    if (loginType == LoginType.phone) {
      if (state.phone.isEmpty) {
        GSEasyLoading.showToast('please_enter_phone'.tr());
        return;
      }
      // Validate phone number length and format
      final bool isValidLength = state.phone.length >= 6;
      // final bool isValidFormat = RegExp(r'^1[3-9]\d{9}$').hasMatch(state.phone); // No need

      if (!isValidLength) {
        GSEasyLoading.showToast('please_enter_correct_phone'.tr());
        return;
      }
      if (state.smsCode.isEmpty) {
        GSEasyLoading.showToast('please_enter_sms_code'.tr());
        return;
      }
    } else if (loginType == LoginType.userName) {
      if (state.username.isEmpty) {
        GSEasyLoading.showToast('please_enter_username'.tr());
        return;
      }
      if (!StringUtil.isValidUsername(state.username)) {
        GSEasyLoading.showToast('username_invalid_rule'.tr());
        return;
      }
    } else if (loginType == LoginType.email) {
      if (state.email.isEmpty) {
        GSEasyLoading.showToast('please_enter_email'.tr());
        return;
      }
      // Validate email format
      final bool isValidEmail = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(state.email);
      if (!isValidEmail) {
        GSEasyLoading.showToast('please_enter_correct_email_format'.tr());
        return;
      }
      // Validate email verification code (stored in verificationCode but will be sent as smsCode)
      if (state.verificationCode == null || state.verificationCode!.isEmpty) {
        GSEasyLoading.showToast('please_enter_email_code'.tr());
        return;
      }
    } else if (loginType == LoginType.google || loginType == LoginType.facebook || loginType == LoginType.tiktok) {
      // Social login validation - privacy policy already checked above
      // Additional social login specific validations can be added here
    }

    // Password validation - skip for phone registration
    if (loginType != LoginType.phone) {
      if (state.password.isEmpty) {
        GSEasyLoading.showToast('please_enter_password'.tr());
        return;
      }
      if (state.password != state.confirmPassword) {
        GSEasyLoading.showToast('passwords_do_not_match'.tr());
        return;
      }
    }
    if (state.inviteCode.isEmpty) {
      GSEasyLoading.showToast('please_enter_invite_code'.tr());
      return;
    }
    if (state.captchaType == CaptchaType.picture && state.verificationCode == null) {
      GSEasyLoading.showToast('please_enter_verification_code'.tr());
      return;
    }

    if (state.captchaType == CaptchaType.wangYi && loginType == LoginType.userName) {
      _showWangYiCaptcha(
        account: state.username,
        onSuccess: (result) {
          fetchRegisterApi(wangYiRes: result, loginType: loginType);
        },
      );
    } else {
      fetchRegisterApi(loginType: loginType);
    }
  }

  /// Get auth method based on login type
  String _getAuthMethod(LoginType? loginType) {
    return loginType?.code.toString() ?? '2'; // Default to account
  }

  void fetchRegisterApi({String? wangYiRes, LoginType? loginType}) async {
    try {
      GSEasyLoading.showLoading();

      // Determine which verification code to use for smsCode parameter
      String? smsCodeToSend;
      if (loginType == LoginType.phone) {
        // For phone registration, use SMS code
        smsCodeToSend = state.smsCode;
      } else if (loginType == LoginType.email) {
        // For email registration, use email verification code through smsCode parameter
        smsCodeToSend = state.verificationCode;
      }

      final result = await UserApi.doRegisterAndLogin(
        userName: loginType == LoginType.phone
            ? state.phone
            : loginType == LoginType.email
                ? state.email
                : state.username,
        password: loginType == LoginType.phone ? '' : state.password,
        // No password for phone registration
        invitationCode: state.inviteCode,
        channelCode: state.channelCode,
        verificationCode: state.verificationCode,
        verificationUUID: state.captchaModel?.uuid,
        wangYiCaptcha: wangYiRes,
        isPhoneNo: loginType == LoginType.phone ? true : null,
        // Pass all verification codes (phone SMS, email verification) through smsCode
        smsCode: smsCodeToSend,
        areaCode: state.selectedCountry?.areaCode,
        currencyId: state.selectCurrency?.id,
        authMethod: _getAuthMethod(loginType),
      );
      GSEasyLoading.dismiss();
      state.verificationCodeController.text = '';
      state.smsCodeController.text = '';
      fetchImageCaptcha();
      if (result.errorStr == null && result.tokenUser != null) {
        autoLogin(result.tokenUser);
        sl<MainScreenCubit>().fetchNoticeDialogListData(); // 获取系统公告弹窗
        emit(state.copyWith(registerStatus: NetState.success));
        await Future.delayed(const Duration(milliseconds: 200));
        sl<NavigatorService>().popUntilOrPush(AppRouter.nav);
        GSEasyLoading.dismiss();
        OpenInstallManager.instance.reportUserRegistration(); // op上报注册成功
      } else {
        GSEasyLoading.showToast(result.errorStr ?? 'registration_failed'.tr());
        emit(state.copyWith(registerStatus: NetState.failed));
      }
    } catch (e) {
      fetchImageCaptcha();
    }
  }

  autoLogin(LoginTokenUser? tokenUser) async {
    if (tokenUser == null) return;
    GSEasyLoading.showLoading(message: 'auto_login_in_progress'.tr());
    sl<UserCubit>().setLoginInfo(tokenUser);
    emit(state.copyWith(registerStatus: NetState.success));
    GSEasyLoading.showToast("login_successful".tr());
  }
}
