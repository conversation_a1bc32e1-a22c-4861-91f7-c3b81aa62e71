import 'package:draggable_float_widget/draggable_float_widget.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:wd/shared/widgets/common_app_bar.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/webview/common_webview.dart';
import 'package:wd/shared/widgets/empty_app_bar.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';

class CommonWebViewPage extends StatefulWidget {
  final String? title;
  final String initialUrl;
  final bool showFloatWidget;

  const CommonWebViewPage({super.key, this.title, required this.initialUrl, this.showFloatWidget = true});

  @override
  State createState() => _CommonWebViewPageState();
}

class _CommonWebViewPageState extends State<CommonWebViewPage> with HideFloatButtonRouteAwareMixin {
  final webViewKey = UniqueKey();

  @override
  void initState() {
    super.initState();
  }

  Widget _buildFloatingButton(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () async {
        CommonDialog.show(
          context: context,
          title: "act_hint".tr(),
          content: "confirm_exit_game".tr(),
          complete: () async {
            sl<NavigatorService>().pop();
          },
        );
      },
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.1),
          shape: BoxShape.circle,
          border: Border.all(
            width: 0.5,
            color: Theme.of(context).dividerColor,
          ),
        ),
        alignment: Alignment.center,
        child: Container(
          width: 18,
          height: 18,
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.redAccent,
      child: Stack(children: [
        PopScope(
          canPop: false,
          child: Scaffold(
              backgroundColor: Colors.white,
              appBar: widget.title != null ? CommonAppBar(pageTitle: widget.title!) : const EmptyAppBar(),
              body: CommonWebView(key: webViewKey, initialUrl: widget.initialUrl)),
        ),
        if (widget.showFloatWidget && widget.title == null) ...[
          if (SystemUtil.isWeb()) ...[
            PointerInterceptor(
              child: _buildFloatingButton(context),
            ),
          ] else ...[
            DraggableFloatWidget(
              // eventStreamController: eventStreamController,
              config: DraggableFloatWidgetBaseConfig(
                isFullScreen: true,
                initPositionYInTop: true,
                initPositionYMarginBorder: 0,
                borderTop: MediaQuery.of(context).padding.top,
                borderBottom: MediaQuery.of(context).padding.bottom,
              ),
              onTap: () {},
              child: _buildFloatingButton(context),
            ),
          ],
        ],
      ]),
    );
  }
}
