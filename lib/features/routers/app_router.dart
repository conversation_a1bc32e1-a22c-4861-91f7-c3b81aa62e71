import 'package:fluro/fluro.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/models/view_models/sponsor_view_model.dart';
import 'package:wd/features/page/0_home/home_lottery/lottery_detail/lottery_detail_cubit.dart';
import 'package:wd/features/page/0_home/home_lottery/lottery_detail/lottery_detail_view.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_detail/video_detail_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_detail/video_detail_view.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_library_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_library_view.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_search/video_search_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_search/video_search_view.dart';
import 'package:wd/features/page/1_game_home/game_sub_list_v2/game_sub_list_v2_cubit.dart';
import 'package:wd/features/page/1_game_home/game_sub_list_v2/game_sub_list_v2_view.dart';
import 'package:wd/features/page/2_activity/activity_detail/activity_detail_cubit.dart';
import 'package:wd/features/page/2_activity/activity_detail/activity_detail_view.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/features/page/2_activity/activity_screen.dart';
import 'package:wd/features/page/2_activity/activity_records_list/activity_records_list_cubit.dart';
import 'package:wd/features/page/2_activity/activity_records_list/activity_records_list_view.dart';
import 'package:wd/features/page/3_chat/red_envelope_detail/red_envelope_detail_cubit.dart';
import 'package:wd/features/page/3_chat/red_envelope_detail/red_envelope_detail_view.dart';
import 'package:wd/features/page/3_chat_new/screens/conversation_screen.dart';
import 'package:wd/features/page/3_transact/topup/topup_cubit.dart';
import 'package:wd/features/page/3_transact/topup/topup_history/top_up_history_detail/top_up_history_detail_view.dart';
import 'package:wd/features/page/3_transact/topup/topup_history/top_up_history_list/top_up_history_list_cubit.dart';
import 'package:wd/features/page/3_transact/topup/topup_history/top_up_history_list/top_up_history_list_view.dart';
import 'package:wd/features/page/3_transact/topup/topup_order/topup_order_cubit.dart';
import 'package:wd/features/page/3_transact/topup/topup_order/topup_order_view.dart';
import 'package:wd/features/page/3_transact/topup/topup_view.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_cubit.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_history/withdraw_history_detail/withdraw_history_detail_view.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_history/withdraw_history_list/withdraw_history_list_cubit.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_history/withdraw_history_list/withdraw_history_list_view.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_history/withdraw_history_list/withdraw_progress_view.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_view.dart';
import 'package:wd/features/page/4_mine/about/about_view.dart';
import 'package:wd/features/page/4_mine/account_security/account_security_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/account_security_view.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/add_bank_card/add_bank_card_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/add_bank_card/add_bank_card_view.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/add_usdt_address/add_usdt_address_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/add_usdt_address/add_usdt_address_view.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/add_wallet/add_wallet_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/add_wallet/add_wallet_view.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/payment_list_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/payment_list_state.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/payment_list_view.dart';
import 'package:wd/features/page/4_mine/announcement/announcement_view.dart';
import 'package:wd/features/page/4_mine/app_share/app_share_cubit.dart';
import 'package:wd/features/page/4_mine/app_share/app_share_view.dart';
import 'package:wd/features/page/4_mine/bet_guide/bet_guide_view.dart';
import 'package:wd/features/page/4_mine/bet_records/bet_records_cubit.dart';
import 'package:wd/features/page/4_mine/bet_records/bet_records_view.dart';
import 'package:wd/features/page/4_mine/course_tutorial/course_tutorials_view.dart';
import 'package:wd/features/page/4_mine/mine_v2_cubit.dart';
import 'package:wd/features/page/4_mine/mine_v3_view.dart';
import 'package:wd/features/page/4_mine/my_rebate/rebate_cubit.dart';
import 'package:wd/features/page/4_mine/my_rebate/rebate_view.dart';
import 'package:wd/features/page/4_mine/my_wallet/my_wallet_cubit.dart';
import 'package:wd/features/page/4_mine/my_wallet/my_wallet_view.dart';
import 'package:wd/features/page/4_mine/notifications/notification_cubit.dart';
import 'package:wd/features/page/4_mine/notifications/notification_view.dart';
import 'package:wd/features/page/4_mine/order/channel_type_order_list/channel_plamform_order_list/channel_platform_order_list_cubit.dart';
import 'package:wd/features/page/4_mine/order/channel_type_order_list/channel_plamform_order_list/channel_platform_order_list_view.dart';
import 'package:wd/features/page/4_mine/order/channel_type_order_list/channel_type_order_list_cubit.dart';
import 'package:wd/features/page/4_mine/order/channel_type_order_list/channel_type_order_list_view.dart';
import 'package:wd/features/page/4_mine/order/order_cubit.dart';
import 'package:wd/features/page/4_mine/order/order_view.dart';
import 'package:wd/features/page/4_mine/profile/profile_cubit.dart';
import 'package:wd/features/page/4_mine/profile/profile_update/update_profile_view.dart';
import 'package:wd/features/page/4_mine/profile/profile_view.dart';
import 'package:wd/features/page/4_mine/promotion_rewards/team_management/team_management_cubit.dart';
import 'package:wd/features/page/4_mine/statement/statement_cubit.dart';
import 'package:wd/features/page/4_mine/statement/statement_view.dart';
import 'package:wd/features/page/4_mine/tutorial/tutorials_view.dart';
import 'package:wd/features/page/4_mine/video_coupon/video_coupon_view.dart';
import 'package:wd/features/page/4_mine/vip_center_v2/vip_center_v2_cubit.dart';
import 'package:wd/features/page/4_mine/vip_center_v2/vip_center_v2_view.dart';
import 'package:wd/features/page/guide/guide_cubit.dart';
import 'package:wd/features/page/guide/guide_view.dart';
import 'package:wd/features/page/login/login_cubit.dart';
import 'package:wd/features/page/login/login_view.dart';
import 'package:wd/features/page/login/register/register_cubit.dart';
import 'package:wd/features/page/main/screens/main_screen_view.dart';
import 'package:wd/features/page/splash/splash_cubit.dart';
import 'package:wd/features/page/splash/splash_view.dart';
import 'package:wd/features/page/webview/common_web_view_page.dart';
import 'package:wd/features/page/webview/game_web_view_page.dart';
import 'package:wd/features/page/webview/html_viewer/common_html_viewer.dart';
import 'package:wd/injection_container.dart';

import '../page/0_tiktok/video_library/popular_video/popular_video_filter/popular_video_filter_view.dart';
import '../../core/models/entities/commit_top_up_entity.dart';
import '../page/0_tiktok/video_library/popular_video/popular_video_search/popular_video_search.dart';
import '../page/0_tiktok/video_library/popular_video/popular_video_search/popular_video_search_cubit.dart';
import '../page/4_mine/agent_recruitment/agent_recruitment_cubit.dart';
import '../page/4_mine/agent_recruitment/agent_recruitment_view.dart';
import '../page/4_mine/promotion_rewards/commission_plan/commission_plan_cubit.dart';
import '../page/4_mine/promotion_rewards/commission_plan/commission_plan_view.dart';
import '../page/4_mine/promotion_rewards/commission_records/commission_records_view.dart';
import '../page/4_mine/promotion_rewards/promotion_rewards_view.dart';
import '../page/4_mine/promotion_rewards/team_management/team_management_view.dart';
import '../page/4_mine/sponsor/sponsor_detail_view.dart';
import '../page/4_mine/sponsor/sponsor_view.dart';
import '../page/4_mine/video_coupon/video_coupon_cubit.dart';
import '../page/login/auth/auth_cubit.dart';
import '../page/login/forgot/forgot_cubit.dart';
import '../page/login/forgot/forgot_screen.dart';
import 'irouter_provider.dart';

class AppRouter extends IRouterProvider {
  static const nav = "/m"; // 主控制器 main
  static const launch = "/l"; // 主控制器 main
  static const guide = "/guide"; // 主控制器 main
  static const login = "/login"; // 登录
  static const register = "/login/register"; // 注册
  static const registerWithInviteCode = "/login/register/:id"; // 注册（带邀请码）
  static const forgot = "/login/forgot"; // 忘记密码
  static const gameListV2 = "/m/games_v2"; // 游戏列表页
  static const gameWebView = "/m/games/detail"; // 游戏网页
  static const commonWebView = "/commonWebView"; // 普通网页（固定屏幕方向）
  static const commonHtmlView = "/commonHtmlView"; // htmlContent（固定屏幕方向）
  static const lottery = "/lottery";
  static const lotteryDetail = "/lottery/detail";
  static const animalGame = "/lottery/animalGame";
  static const videoLibrary = "/video/library";
  static const videoDetail = "/video/detail";
  static const videoSearch = "/video/search";

  static const activity = "/activity"; // 活动列表
  static const activityDetail = "/activity/detail"; // 活动详情
  static const activityRecords = "/activity/records"; // 活动详情

  static const recharge = "/transact/recharge"; // 充值
  static const withdraw = "/transact/withdraw"; // 充值
  static const transactTopUpHistoryList = "/transact/topUpHistory"; // 充提-充值记录列表

  static const transactTopUpHistoryDetail = "/transact/topUpHistory/detail"; // 充提-充值记录-详情
  static const transactWithdrawHistoryList = "/transact/withdrawHistory"; // 充提-提现记录
  static const transactWithdrawHistoryDetail = "/transact/withdrawHistory/detail"; // 充提-提现记录
  static const transactWithdrawProgress = "/transact/withdrawHistory/progress"; // 充提-提现进度

  static const topUpOrderDetail = "/transact/topUp/detail"; // 充提-充值订单

  static const order = "/order"; // 注单
  static const orderChannelRecordList = "/order/channel"; // 注单-类型统计 (比如：电子)
  static const orderChannelDetailRecordList = "/order/channel/detail"; // 注单-类型统计-平台 （比如：电子-PG）
  static const notifications = "/notifications"; // Notification

  // static const chatHome = "/chat"; // 聊天室
  static const tencentChat = "/chatroom"; // 腾讯聊天室
  static const chatRedEnvelopeDetail = "/chatroom/redEnvelope/detail"; // 腾讯聊天室-红包

  static const user = "/mine"; // 我的
  static const userProfile = "/mine/profile"; // 用户-个人资料
  static const userStatement = "/mine/statement"; // 用户-个人账变
  static const userAccountSecurity = "/mine/security"; // 用户-安全中心
  static const userModifyPwd = "/mine/security/modifyPwd"; // 用户-安全中心
  static const updateProfile = "/mine/profile/update"; // 用户-个人资料
  static const vipCenter = "/mine/vip"; // 用户-Vip中心
  static const userWithdrawList = "/mine/security/withdrawList"; // 用户-安全中心-提现银行卡列表
  static const userBankAccountAdd = "/mine/security/withdrawList/bank"; // 用户-安全中心-提现银行卡列表-添加银行卡
  // static const userPaymentWalletList = "/mine/security/walletList"; // 用户-安全中心-提现钱包列表
  static const userPaymentWalletAdd = "/mine/security/withdrawList/wallet"; // 用户-安全中心-提现钱包列表-添加钱包
  // static const userPaymentUsdtList = "/mine/security/usdtList"; // 用户-安全中心-提现USDT列表
  static const userPaymentUsdtAdd = "/mine/security/withdrawList/usdt"; // 用户-安全中心-提现USDT列表-添加钱包
  static const myWallet = "/mine/wallet"; // 用户-我的钱包
  static const tutorialList = "/mine/tutorialList"; // 用户-新手教程
  static const aboutList = "/mine/aboutList"; // 用户-关于我们
  static const courseTutorialList = "/mine/course/tutorialList"; // 用户-新手教程
  static const betRecordList = "/mine/bet/recordList"; // 用户-新手教程
  static const sponsorList = "/mine/sponsorList"; // 用户-官方赞助列表
  static const sponsorDetail = "/mine/sponsorDetail"; // 用户-官方赞助详情
  static const announcementList = "/mine/announcementList"; // 用户-公告列表
  static const betGuidesList = "/mine/betGuidesList"; // 用户-投注指南列表
  static const myRebate = "/mine/rebate"; // 用户-我的返利
  static const promotionRewards = "/mine/promotionRewards"; // 用户-推广赚钱
  static const commissionRecords = "/mine/promotionRecords"; // 用户-推广记录
  static const teamManagement = "/mine/teamManagement"; // 用户-团队管理
  static const commisionRules = "/mine/commisionRules"; // 用户-佣金规则
  static const appShare = "/appShare"; // 邀请好友界面
  static const popularVideoFilter = "/popularVideoFilter"; // 热门视频筛选
  static const videoCoupon = "/mine/videoCoupon"; // 邀请好友界面
  static const popularVideoSearch = "/popularVideoSearch"; // 热门视频搜索
  static const agentRecruitment = "/mine/agentRecruitment"; // 代理招募
  @override
  void initRouter(FluroRouter router) {
    /// 主控制器
    router.define(nav, handler: Handler(handlerFunc: (context, params) {
      return const MainScreenPage();
    }));

    /// 启动页
    router.define(launch, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => sl<SplashCubit>(),
        child: const SplashPage(),
      );
    }));

    /// 引导页
    router.define(guide, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => sl<GuideCubit>(),
        child: const GuidePage(),
      );
    }));

    router.define(activity, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => sl<ActivityListCubit>(),
        child: const ActivityScreen(),
      );
    }));

    router.define(user, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => sl<MineV2Cubit>(),
        child: const MineV3Page(),
      );
    }));

    router.define(login, handler: Handler(handlerFunc: (context, params) {
      return MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => LoginCubit()),
          BlocProvider(create: (context) => AuthCubit()),
          BlocProvider(create: (context) => RegisterCubit()),
        ],
        child: const LoginPage(),
      );
    }));

    router.define(register, handler: Handler(handlerFunc: (context, params) {
      return MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => LoginCubit()),
          BlocProvider(create: (context) => AuthCubit()..switchAuthType(AuthType.register)),
          BlocProvider(create: (context) => RegisterCubit()),
        ],
        child: const LoginPage(),
      );
    }));

    router.define(registerWithInviteCode, handler: Handler(handlerFunc: (context, params) {
      final String? inviteCode = params['id']?.first;
      return MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => LoginCubit()),
          BlocProvider(create: (context) => AuthCubit()..switchAuthType(AuthType.register)),
          BlocProvider(create: (context) => RegisterCubit()),
        ],
        child: LoginPage(inviteCode: inviteCode),
      );
    }));

    router.define(forgot, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => ForgotCubit(),
        child: const ForgotPage(),
      );
    }));

    router.define(gameListV2, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      final GameTypeV2 gameTypeModel = argument?['gameType'];

      return BlocProvider(
        create: (context) => GameSubListV2Cubit(GamePlatformV2.createAllGame(gameTypeModel.code)),
        child: GameSubListV2Page(gameTypeModel: gameTypeModel),
      );
    }));

    router.define(gameWebView, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      String url = argument?['url'];
      return GameWebViewPage(initialUrl: url);
    }));

    // router.define(lottery, handler: Handler(handlerFunc: (context, params) {
    //   return BlocProvider(
    //     create: (context) => HomeLotteryCubit(),
    //     child: const HomeLotteryPage(),
    //   );
    // }));

    router.define(lotteryDetail, handler: Handler(handlerFunc: (context, params) {
      Map map = context!.settings!.arguments as Map;
      List groupList = map['list'];
      return BlocProvider(
        create: (context) => LotteryDetailCubit(),
        child: LotteryDetailPage(
          model: map['model'],
          lotteryGroupList: List.from(groupList.cast<LotteryGroup>()),
        ),
      );
    }));

    router.define(videoLibrary, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => VideoLibraryCubit(),
        child: const VideoLibraryPage(),
      );
    }));

    router.define(videoDetail, handler: Handler(handlerFunc: (context, params) {
      Map map = context!.settings!.arguments as Map;
      return BlocProvider(
        create: (context) => VideoDetailCubit(),
        child: VideoDetailPage(
          model: map['model'],
          videoCategory: map['videoCategory'],
          pageTitle: map['pageTitle'],
        ),
      );
    }));

    router.define(videoSearch, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => VideoSearchCubit(),
        child: const VideoSearchPage(),
      );
    }));

    router.define(popularVideoSearch, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => PopularVideoSearchCubit(),
        child: const PopularVideoSearchPage(),
      );
    }));

    router.define(activityDetail, handler: Handler(handlerFunc: (context, params) {
      ActivityRecords argument = context!.settings!.arguments as ActivityRecords;
      return BlocProvider(
        create: (context) => ActivityDetailCubit(),
        child: ActivityDetailPage(model: argument),
      );
    }));

    router.define(activityRecords, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => ActivityRecordsListCubit(),
        child: const ActivityRecordsListPage(),
      );
    }));

    router.define(recharge, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => TopupCubit(),
        child: const TopupPage(),
      );
    }));

    router.define(withdraw, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => WithdrawCubit(),
        child: const WithdrawPage(),
      );
    }));

    router.define(transactTopUpHistoryList, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => TopUpHistoryListCubit(),
        child: const TopUpHistoryListPage(),
      );
    }));

    router.define(transactTopUpHistoryDetail, handler: Handler(handlerFunc: (context, params) {
      TopUpRecord model = context!.settings!.arguments as TopUpRecord;
      return TopUpHistoryDetailPage(model: model);
    }));

    router.define(transactWithdrawHistoryList, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => WithdrawHistoryListCubit(),
        child: const WithdrawHistoryListPage(),
      );
    }));

    router.define(transactWithdrawHistoryDetail, handler: Handler(handlerFunc: (context, params) {
      WithdrawRecord model = context!.settings!.arguments as WithdrawRecord;
      return WithdrawHistoryDetailPage(model: model);
    }));

    router.define(transactWithdrawProgress, handler: Handler(
      handlerFunc: (context, params) {
        return BlocProvider(
          create: (context) => WithdrawHistoryListCubit(type: RecordDateType.week),
          child: const WithdrawProgressPage(),
        );
      },
    ));

    router.define(topUpOrderDetail, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      CommitTopUpEntity model = argument?['model'] as CommitTopUpEntity;
      int channelId = argument?['channelId'];
      return BlocProvider(
        create: (context) => TopupOrderCubit(),
        child: TopupOrderPage(model: model, channelId: channelId),
      );
    }));

    router.define(commonWebView, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      String? title = argument?['title'];
      String url = argument?['url'];
      return CommonWebViewPage(
        title: title,
        initialUrl: url,
        showFloatWidget: argument?['showFloatWidget'] ?? true,
      );
    }));

    router.define(commonHtmlView, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      String? title = argument?['title'];
      String? path = argument?['path'];
      String? content = argument?['content'];
      return CommonHtmlViewer(
        assetPath: path,
        title: title,
        htmlContent: content,
      );
    }));

    router.define(order, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => OrderCubit(),
        child: const OrderPage(),
      );
    }));

    router.define(orderChannelRecordList, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      String gameClassCode = argument?['gameClassCode'];
      String gameClassName = argument?['gameClassName'];
      RecordDateType dateType = argument?['dateType'];
      String returnAmount = argument?['returnAmount'];
      return BlocProvider(
        create: (context) => ChannelTypeOrderListCubit(),
        child: ChannelTypeOrderListPage(
          gameClassCode: gameClassCode,
          gameClassName: gameClassName,
          dateType: dateType,
          returnAmount: returnAmount,
        ),
      );
    }));

    router.define(orderChannelDetailRecordList, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      String gameClassCode = argument?['gameClassCode'];
      String thirdPlatformId = argument?['thirdPlatformId'];
      String categoryCode = argument?['categoryCode'];
      String gameClassName = argument?['gameClassName'];
      RecordDateType dateType = argument?['dateType'];
      return BlocProvider(
        create: (context) => ChannelPlatformOrderListCubit(),
        child: ChannelPlatformOrderListPage(
          gameClassCode: gameClassCode,
          categoryCode: categoryCode,
          thirdPlatformId: thirdPlatformId,
          gameClassName: gameClassName,
          dateType: dateType,
        ),
      );
    }));

    router.define(notifications, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider.value(
        value: sl<NotificationsCubit>(),
        child: const NotificationsListView(),
      );
    }));

    router.define(tencentChat, handler: Handler(handlerFunc: (context, params) {
      //! temporary bypass
      // return ChatMainPage();
      return const ConversationScreen();
      // return BlocProvider(create: (context) => ChatCubit(), child: ChatPage());
    }));

    router.define(chatRedEnvelopeDetail, handler: Handler(handlerFunc: (context, params) {
      final args = context!.settings!.arguments as Map;
      return BlocProvider(
          create: (context) => RedEnvelopeDetailCubit(),
          child: RedEnvelopeDetailPage(
            title: args['title'],
            content: args['content'],
            amount: args['amount'],
          ));
    }));

    router.define(userProfile, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => UserProfileCubit(),
        child: const UserProfilePage(),
      );
    }));

    router.define(userStatement, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => StatementCubit(),
        child: const StatementPage(),
      );
    }));

    router.define(userAccountSecurity, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => AccountSecurityCubit(),
        child: const AccountSecurityPage(),
      );
    }));

    router.define(updateProfile, handler: Handler(handlerFunc: (context, params) {
      final args = context!.settings!.arguments as Map;
      return UpdateProfileView(
        initialValue: args['initialValue'] as String,
        field: args['field'] as UserFieldType,
      );
    }));

    router.define(vipCenter, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => VipCenterV2Cubit(),
        child: const VipCenterV2Page(),
      );
    }));

    router.define(userModifyPwd, handler: Handler(handlerFunc: (context, params) {
      final type = context!.settings!.arguments as SetPasswordType;
      return BlocProvider(
        create: (context) => ModifyPwdCubit(),
        child: ModifyPwdPage(type: type),
      );
    }));

    router.define(userWithdrawList, handler: Handler(handlerFunc: (context, params) {
      final type = context!.settings!.arguments as PaymentType;
      return BlocProvider(
        create: (context) => PaymentListCubit(type),
        child: const PaymentListView(),
      );
    }));

    router.define(userBankAccountAdd, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => AddBankCardCubit(),
        child: const AddBankCardPage(),
      );
    }));

    router.define(userPaymentWalletAdd, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => AddWalletCubit(),
        child: const AddWalletPage(),
      );
    }));

    router.define(userPaymentUsdtAdd, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => AddUsdtAddressCubit(),
        child: const AddUsdtAddressPage(),
      );
    }));

    router.define(myWallet, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => MyWalletCubit(),
        child: const MyWalletPage(),
      );
    }));

    router.define(tutorialList, handler: Handler(handlerFunc: (context, params) {
      return const TutorialListPage();
    }));

    router.define(courseTutorialList, handler: Handler(handlerFunc: (context, params) {
      return const CourseTutorialListPage();
    }));

    router.define(aboutList, handler: Handler(handlerFunc: (context, params) {
      return const AboutListPage();
    }));

    router.define(betRecordList, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => BetRecordListCubit(),
        child: const BetRecordListPage(),
      );
    }));

    router.define(sponsorList, handler: Handler(handlerFunc: (context, params) {
      return const SponsorListPage();
    }));

    router.define(sponsorDetail, handler: Handler(handlerFunc: (context, params) {
      SponsorItem argument = context!.settings!.arguments as SponsorItem;
      return SponsorDetailPage(sponsor: argument);
    }));

    router.define(announcementList, handler: Handler(handlerFunc: (context, params) {
      return const AnnouncementListPage();
    }));

    router.define(betGuidesList, handler: Handler(handlerFunc: (context, params) {
      return const BetGuidesListPage();
    }));

    router.define(myRebate, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => RebateCubit(),
        child: const RebateView(),
      );
    }));

    router.define(promotionRewards, handler: Handler(handlerFunc: (context, params) {
      return const PromotionRewardsView();
    }));

    router.define(commissionRecords, handler: Handler(handlerFunc: (context, params) {
      return const CommissionRecordsView();
    }));

    router.define(teamManagement, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(create: (context) => TeamManagementCubit(), child: const TeamManagementView());
    }));

    router.define(commisionRules, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => CommissionPlanCubit(),
        child: const CommissionPlanView(),
      );
    }));

    router.define(appShare, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => AppShareCubit(),
        child: const AppSharePage(),
      );
    }));

    router.define(popularVideoFilter, handler: Handler(handlerFunc: (context, params) {
      Map? argument = context!.settings!.arguments as Map?;
      final category = argument?['category'];
      return BlocProvider(
        create: (context) => VideoLibraryCubit(),
        child: PopularVideoFilterView(animationCount: 10, category: category),
      );
    }));

    router.define(videoCoupon, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => VideoCouponCubit(),
        child: const VideoCouponPage(),
      );
    }));

    router.define(agentRecruitment, handler: Handler(handlerFunc: (context, params) {
      return BlocProvider(
        create: (context) => AgentRecruitmentCubit(),
        child: const AgentRecruitmentView(),
      );
    }));
  }
}
